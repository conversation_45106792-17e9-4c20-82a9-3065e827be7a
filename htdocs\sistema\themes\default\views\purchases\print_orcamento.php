<?php (defined('BASEPATH')) OR exit('No direct script access allowed'); ?>
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title><?= $page_title . " " . $purchase->reference; ?></title>
    <meta http-equiv="cache-control" content="max-age=0"/>
    <meta http-equiv="cache-control" content="no-cache"/>
    <meta http-equiv="expires" content="0"/>
    <meta http-equiv="pragma" content="no-cache"/>
    <link rel="shortcut icon" href="<?= $assets ?>images/icon.png"/>
    <link href="<?= $assets ?>styles/theme.css" rel="stylesheet"/>
    <style type="text/css" media="all">
        body {
            color: #000;
            font-size: 12px;
        }

        #wrapper {
            max-width: 780px;
            margin: 0 auto;
            padding-top: 20px;
        }

        .btn {
            border-radius: 0;
            margin-bottom: 5px;
        }

        .table {
            border-radius: 3px;
        }

        .table th {
            background: #f5f5f5;
        }

        .table th, .table td {
            vertical-align: middle !important;
        }

        h3 {
            margin: 5px 0;
        }

        .print-only {
            display: none;
        }

        @media print {
            .no-print {
                display: none;
            }

            .print-only {
                display: block;
            }

            #wrapper {
                max-width: 780px;
                width: 100%;
                min-width: 250px;
                margin: 0 auto;
            }

            .page-break {
                page-break-after: always;
                page-break-inside: avoid;
            }
        }
    </style>
</head>
<body>
<div id="wrapper">
    <div id="receiptData" style="width: 666px; margin: 0 auto; padding: 20px;">
        <div class="text-center">
            <h3 style="text-transform:uppercase;"><?= $Settings->site_name; ?></h3>
            <?php
            echo "<p>" . $Settings->address . " " . $Settings->city . " " . $Settings->postal_code . " " . $Settings->state . " " . $Settings->country .
                "<br>" . lang("tel") . ": " . $Settings->phone . "</p>";
            ?>
        </div>
        <div class="row">
            <div class="col-sm-6">
                <p style="font-weight:bold;"><?= lang("to"); ?>:</p>
                <h3><?= $supplier->company ? $supplier->company : $supplier->name; ?></h3>
                <?= $supplier->company ? "" : "Attn: " . $supplier->name ?>
                <?php
                echo $supplier->address . "<br>" . $supplier->city . " " . $supplier->postal_code . " " . $supplier->state . "<br>" . $supplier->country;
                echo "<p>";
                if ($supplier->vat_no != "-" && $supplier->vat_no != "") {
                    echo "<br>" . lang("vat_no") . ": " . $supplier->vat_no;
                }
                if ($supplier->cf1 != "-" && $supplier->cf1 != "") {
                    echo "<br>" . lang("scf1") . ": " . $supplier->cf1;
                }
                if ($supplier->cf2 != "-" && $supplier->cf2 != "") {
                    echo "<br>" . lang("scf2") . ": " . $supplier->cf2;
                }
                if ($supplier->cf3 != "-" && $supplier->cf3 != "") {
                    echo "<br>" . lang("scf3") . ": " . $supplier->cf3;
                }
                if ($supplier->cf4 != "-" && $supplier->cf4 != "") {
                    echo "<br>" . lang("scf4") . ": " . $supplier->cf4;
                }
                if ($supplier->cf5 != "-" && $supplier->cf5 != "") {
                    echo "<br>" . lang("scf5") . ": " . $supplier->cf5;
                }
                if ($supplier->cf6 != "-" && $supplier->cf6 != "") {
                    echo "<br>" . lang("scf6") . ": " . $supplier->cf6;
                }
                echo "</p>";
                echo lang("tel") . ": " . $supplier->phone . "<br>" . lang("email") . ": " . $supplier->email;
                ?>
            </div>
            <div class="col-sm-6">
                <p style="font-weight:bold;"><?= lang("date"); ?>: <?= $this->sma->hrld($purchase->date); ?></p>
                <p style="font-weight:bold;"><?= lang("ref"); ?>: <?= $purchase->reference; ?></p>
                <?php if (!empty($purchase->status)) { ?>
                    <p style="font-weight:bold;"><?= lang("status"); ?>: <?= lang($purchase->status); ?></p>
                <?php } ?>
            </div>
        </div>

        <div class="table-responsive">
            <table class="table table-bordered table-hover table-striped">
                <thead>
                <tr>
                    <th><?= lang("no"); ?></th>
                    <th><?= lang("description"); ?></th>
                    <th><?= lang("quantity"); ?></th>
                    <th><?= lang("unit_cost"); ?></th>
                    <?php
                    if ($Settings->tax1) {
                        echo '<th>' . lang("tax") . '</th>';
                    }
                    if ($Settings->product_discount) {
                        echo '<th>' . lang("discount") . '</th>';
                    }
                    ?>
                    <th><?= lang("subtotal"); ?></th>
                </tr>
                </thead>
                <tbody>
                <?php
                $r = 1;
                foreach ($rows as $row):
                    ?>
                    <tr>
                        <td style="text-align:center; width:40px; vertical-align:middle;"><?= $r; ?></td>
                        <td style="vertical-align:middle;">
                            <?= $row->product_code . " - " . $row->product_name . ($row->variant ? ' (' . $row->variant . ')' : ''); ?>
                            <?= $row->details ? '<br>' . $row->details : ''; ?>
                        </td>
                        <td style="width: 80px; text-align:center; vertical-align:middle;"><?= $this->sma->formatQuantity($row->quantity); ?></td>
                        <td style="text-align:right; width:100px;"><?= $this->sma->formatMoney($row->unit_cost); ?></td>
                        <?php
                        if ($Settings->tax1) {
                            echo '<td style="width: 100px; text-align:right; vertical-align:middle;">' . ($row->item_tax != 0 && $row->tax_code ? '<small>(' . $row->tax_code . ')</small> ' : '') . $this->sma->formatMoney($row->item_tax) . '</td>';
                        }
                        if ($Settings->product_discount) {
                            echo '<td style="width: 100px; text-align:right; vertical-align:middle;">' . ($row->discount != 0 ? '<small>(' . $row->discount . ')</small> ' : '') . $this->sma->formatMoney($row->item_discount) . '</td>';
                        }
                        ?>
                        <td style="text-align:right; width:120px;"><?= $this->sma->formatMoney($row->subtotal); ?></td>
                    </tr>
                    <?php
                    $r++;
                endforeach;
                ?>
                </tbody>
                <tfoot>
                <tr>
                    <td colspan="4" style="text-align:right;"><?= lang("total"); ?></td>
                    <?php
                    if ($Settings->tax1) {
                        echo '<td style="text-align:right;">' . $this->sma->formatMoney($purchase->product_tax) . '</td>';
                    }
                    if ($Settings->product_discount) {
                        echo '<td style="text-align:right;">' . $this->sma->formatMoney($purchase->product_discount) . '</td>';
                    }
                    ?>
                    <td style="text-align:right;"><?= $this->sma->formatMoney($purchase->total); ?></td>
                </tr>
                <?php if ($purchase->order_discount != 0) { ?>
                    <tr>
                        <td colspan="<?= $tcol; ?>" style="text-align:right;"><?= lang("order_discount"); ?></td>
                        <td style="text-align:right;"><?= $this->sma->formatMoney($purchase->order_discount); ?></td>
                    </tr>
                <?php } ?>
                <?php if ($Settings->tax2 && $purchase->order_tax != 0) { ?>
                    <tr>
                        <td colspan="<?= $tcol; ?>" style="text-align:right;"><?= lang("order_tax"); ?></td>
                        <td style="text-align:right;"><?= $this->sma->formatMoney($purchase->order_tax); ?></td>
                    </tr>
                <?php } ?>
                <?php if ($purchase->shipping != 0) { ?>
                    <tr>
                        <td colspan="<?= $tcol; ?>" style="text-align:right;"><?= lang("shipping"); ?></td>
                        <td style="text-align:right;"><?= $this->sma->formatMoney($purchase->shipping); ?></td>
                    </tr>
                <?php } ?>
                <tr>
                    <td colspan="<?= $tcol; ?>" style="text-align:right; font-weight:bold;"><?= lang("total_amount"); ?></td>
                    <td style="text-align:right; font-weight:bold;"><?= $this->sma->formatMoney($purchase->grand_total); ?></td>
                </tr>
                </tfoot>
            </table>
        </div>

        <div class="row">
            <div class="col-xs-12">
                <?php if ($purchase->note || $purchase->note != "") { ?>
                    <div class="well well-sm">
                        <p class="bold"><?= lang("note"); ?>:</p>

                        <div><?= $this->sma->decode_html($purchase->note); ?></div>
                    </div>
                <?php } ?>
            </div>
            <div class="col-xs-4 pull-left">
                <p><?= lang("created_by"); ?>: <?= $created_by->first_name . ' ' . $created_by->last_name; ?> </p>

                <p>&nbsp;</p>

                <p>&nbsp;</p>
                <hr>
                <p><?= lang("stamp_sign"); ?></p>
            </div>
            <div class="col-xs-4 col-xs-offset-1 pull-right">
                <p><?= lang("received_by"); ?>: </p>

                <p>&nbsp;</p>

                <p>&nbsp;</p>
                <hr>
                <p><?= lang("stamp_sign"); ?></p>
            </div>
        </div>

        <div class="buttons">
            <div class="btn-group btn-group-justified">
                <div class="btn-group">
                    <a href="<?= site_url('purchases/orcamentos') ?>" class="tip btn btn-primary" title="<?= lang('back_to_list') ?>">
                        <i class="fa fa-arrow-left"></i> <span class="hidden-sm hidden-xs"><?= lang('back_to_list') ?></span>
                    </a>
                </div>
                <div class="btn-group">
                    <a href="<?= site_url('purchases/edit_orcamento/' . $purchase->id) ?>" class="tip btn btn-warning" title="<?= lang('edit_orcamento') ?>">
                        <i class="fa fa-edit"></i> <span class="hidden-sm hidden-xs"><?= lang('edit_orcamento') ?></span>
                    </a>
                </div>
                <div class="btn-group">
                    <a href="<?= site_url('purchases/convert_to_purchase/' . $purchase->id) ?>" class="tip btn btn-success" title="<?= lang('convert_to_purchase') ?>">
                        <i class="fa fa-exchange"></i> <span class="hidden-sm hidden-xs"><?= lang('convert_to_purchase') ?></span>
                    </a>
                </div>
                <div class="btn-group">
                    <a href="#" onclick="window.print();return false;" class="tip btn btn-info" title="<?= lang('print') ?>">
                        <i class="fa fa-print"></i> <span class="hidden-sm hidden-xs"><?= lang('print') ?></span>
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>
</body>
</html>