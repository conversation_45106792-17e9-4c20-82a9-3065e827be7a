<?php (defined('BASEPATH')) OR exit('No direct script access allowed'); ?>

<div class="box">
    <div class="box-header">
        <h2 class="blue"><i class="fa-fw fa fa-edit"></i><?= lang('edit_orcamento'); ?></h2>
    </div>
    <div class="box-content">
        <div class="row">
            <div class="col-lg-12">

                <p class="introtext"><?php echo lang('enter_info'); ?></p>

                <?php
                $attrib = array('data-toggle' => 'validator', 'role' => 'form', 'class' => 'edit-po-form');
                echo form_open_multipart("purchases/edit_orcamento/" . $purchase->id, $attrib)
                ?>

                <div class="row">
                    <div class="col-lg-12">

                        <div class="col-md-4">
                            <div class="form-group">
                                <?= lang("date", "podate"); ?>
                                <?php echo form_input('date', (isset($_POST['date']) ? $_POST['date'] : $purchase->date), 'class="form-control input-tip datetime" id="podate" required="required"'); ?>
                            </div>
                        </div>

                        <div class="col-md-4">
                            <div class="form-group">
                                <?= lang("reference", "poref"); ?>
                                <?php echo form_input('reference', (isset($_POST['reference']) ? $_POST['reference'] : $purchase->reference), 'class="form-control input-tip" id="poref" required="required"'); ?>
                            </div>
                        </div>

                        <div class="col-md-4">
                            <div class="form-group">
                                <?= lang("supplier", "posupplier"); ?>
                                <div class="input-group">
                                    <?php
                                    $sp["id"] = "0";
                                    $sp["text"] = "";
                                    $sp["name"] = lang('select').' '.lang('supplier');
                                    $suppliers = array_merge(array($sp), $suppliers);
                                    $sup = array();
                                    foreach ($suppliers as $supplier) {
                                        $sup[$supplier->id] = $supplier->name;
                                    }
                                    echo form_dropdown('supplier', $sup, (isset($_POST['supplier']) ? $_POST['supplier'] : $purchase->supplier_id), 'id="posupplier" class="form-control input-tip select" data-placeholder="' . lang("select") . ' ' . lang("supplier") . '" required="required" style="width:100%;"');
                                    ?>
                                    <div class="input-group-addon no-print" style="padding: 2px 5px;">
                                        <a href="#" id="addSupplier" class="external" data-toggle="modal" data-target="#myModal">
                                            <i class="fa fa-2x fa-plus-circle" id="addIcon"></i>
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="col-md-12">
                            <div class="clearfix"></div>
                            <div class="well well-sm">
                                <a href="#" id="addManually" class="tip" title="<?= lang('add_product_manually') ?>">
                                    <i class="fa fa-2x fa-plus-circle"></i>
                                </a>
                                <div class="form-group" style="margin-bottom:0;">
                                    <div class="input-group wide-tip">
                                        <div class="input-group-addon" style="padding-left: 10px; padding-right: 10px;">
                                            <i class="fa fa-2x fa-barcode addIcon"></i></a></div>
                                        <?php echo form_input('add_item', '', 'class="form-control input-lg" id="add_item" placeholder="' . lang('add_product_to_order') . '"'); ?>
                                    </div>
                                </div>
                                <div class="clearfix"></div>
                            </div>
                        </div>

                        <div class="col-md-12">
                            <div class="control-group table-group">
                                <label class="table-label"><?= lang("order_items"); ?> *</label>

                                <div class="controls table-controls">
                                    <table id="poTable" class="table items table-striped table-bordered table-condensed table-hover">
                                        <thead>
                                        <tr>
                                            <th class="col-md-4"><?= lang("product") . ' (' . lang("code") . ' - ' . lang("name") . ')'; ?></th>
                                            <th class="col-md-1"><?= lang("quantity"); ?></th>
                                            <th class="col-md-1"><?= lang("unit_cost"); ?></th>
                                            <th class="col-md-1"><?= lang("discount"); ?></th>
                                            <th class="col-md-1"><?= lang("tax"); ?></th>
                                            <th class="col-md-1"><?= lang("subtotal"); ?></th>
                                            <th style="width: 30px !important; text-align: center;"><i class="fa fa-trash-o"
                                                                                                       style="opacity:0.5; filter:alpha(opacity=50);"></i>
                                            </th>
                                        </tr>
                                        </thead>
                                        <tbody></tbody>
                                        <tfoot></tfoot>
                                    </table>
                                </div>
                            </div>
                        </div>

                        <div class="col-md-4">
                            <div class="form-group">
                                <?= lang("order_discount", "podiscount"); ?>
                                <?php echo form_input('order_discount', (isset($_POST['order_discount']) ? $_POST['order_discount'] : $purchase->order_discount), 'class="form-control input-tip" id="podiscount"'); ?>
                            </div>
                        </div>

                        <div class="col-md-4">
                            <div class="form-group">
                                <?= lang("order_tax", "potax2"); ?>
                                <?php
                                $tr["id"] = "0";
                                $tr["text"] = "";
                                $tr["name"] = lang('select').' '.lang('order_tax');
                                $tax_rates = array_merge(array($tr), $tax_rates);
                                $tx = array();
                                foreach ($tax_rates as $tax) {
                                    $tx[$tax->id] = $tax->name;
                                }
                                echo form_dropdown('order_tax', $tx, (isset($_POST['order_tax']) ? $_POST['order_tax'] : $purchase->order_tax_id), 'id="potax2" class="form-control input-tip select" data-placeholder="' . lang("select") . ' ' . lang("order_tax") . '" style="width:100%;"');
                                ?>
                            </div>
                        </div>

                        <div class="col-md-4">
                            <div class="form-group">
                                <?= lang("shipping", "poshipping"); ?>
                                <?php echo form_input('shipping', (isset($_POST['shipping']) ? $_POST['shipping'] : $purchase->shipping), 'class="form-control input-tip" id="poshipping"'); ?>
                            </div>
                        </div>

                        <div class="col-md-12">
                            <div class="form-group">
                                <?= lang("note", "ponote"); ?>
                                <?php echo form_textarea('note', (isset($_POST['note']) ? $_POST['note'] : $purchase->note), 'class="form-control" id="ponote" style="margin-top: 10px; height: 100px;"'); ?>
                            </div>
                        </div>
                        <div class="col-md-12">
                            <div class="from-group">
                                <button type="submit" class="btn btn-primary" id="submit_purchase"><?= lang('submit') ?></button>
                                <button type="button" class="btn btn-danger" id="reset"><?= lang('reset') ?></button>
                            </div>
                        </div>
                    </div>
                </div>
                <div id="bottom-total" class="well well-sm" style="margin-bottom: 0;">
                    <table class="table table-bordered table-condensed totals" style="margin-bottom:0;">
                        <tr class="warning">
                            <td><?= lang('items') ?> <span class="totals_val pull-right" id="titems">0</span></td>
                            <td><?= lang('total') ?> <span class="totals_val pull-right" id="total">0.00</span></td>
                            <td><?= lang('order_discount') ?> <span class="totals_val pull-right" id="tds">0.00</span></td>
                            <td><?= lang('order_tax') ?> <span class="totals_val pull-right" id="ttax2">0.00</span></td>
                            <td><?= lang('shipping') ?> <span class="totals_val pull-right" id="tship">0.00</span></td>
                            <td><?= lang('grand_total') ?> <span class="totals_val pull-right" id="gtotal">0.00</span></td>
                        </tr>
                    </table>
                </div>

                <?php echo form_close(); ?>

            </div>

        </div>
    </div>
</div>

<script type="text/javascript">
    var count = 1, an = 1, po_edit = true, product_variant = 0, shipping = 0, product_tax = 0, invoice_tax = 0, total = 0, tax_rates = <?php echo json_encode($tax_rates); ?>, poitems = {}, audio_success = new Audio('<?= $assets ?>sounds/sound2.mp3'), audio_error = new Audio('<?= $assets ?>sounds/sound3.mp3');
    $(document).ready(function () {
        <?php if ($this->input->get('supplier')) { ?>
        if (!localStorage.getItem('poitems')) {
            localStorage.setItem('posupplier', <?=$this->input->get('supplier');?>);
        }
        <?php } ?>
        <?php if ($Owner || $Admin) { ?>
        if (!localStorage.getItem('podate')) {
            $("#podate").datetimepicker({
                format: site.dateFormats.js_ldate,
                fontAwesome: true,
                language: 'sma',
                weekStart: 1,
                todayBtn: 1,
                autoclose: 1,
                todayHighlight: 1,
                startView: 2,
                forceParse: 0
            }).datetimepicker('update', new Date());
        }
        $(document).on('change', '#podate', function (e) {
            localStorage.setItem('podate', $(this).val());
        });
        if (podate = localStorage.getItem('podate')) {
            $('#podate').val(podate);
        }
        <?php } ?>
        $('#potax2').change(function (e) {
            localStorage.setItem('potax2', $(this).val());
            $('#potax2').val(localStorage.getItem('potax2'));
        });
        if (potax2 = localStorage.getItem('potax2')) {
            $('#potax2').select2("val", potax2);
        }
        $('#poshipping').change(function (e) {
            localStorage.setItem('poshipping', $(this).val());
        });
        if (poshipping = localStorage.getItem('poshipping')) {
            $('#poshipping').val(poshipping);
        }
        $('#popayment_term').change(function (e) {
            localStorage.setItem('popayment_term', $(this).val());
        });
        if (popayment_term = localStorage.getItem('popayment_term')) {
            $('#popayment_term').val(popayment_term);
        }
        $('#posupplier').change(function (e) {
            localStorage.setItem('posupplier', $(this).val());
            $('#supplier_id').val($(this).val());
        });
        if (posupplier = localStorage.getItem('posupplier')) {
            $('#posupplier').val(posupplier).select2({
                minimumInputLength: 1,
                data: [],
                initSelection: function (element, callback) {
                    $.ajax({
                        type: "get", async: false,
                        url: site.base_url + "suppliers/getSupplier/" + $(element).val(),
                        dataType: "json",
                        success: function (data) {
                            callback(data[0]);
                        }
                    });
                },
                ajax: {
                    url: site.base_url + "suppliers/suggestions",
                    dataType: 'json',
                    quietMillis: 15,
                    data: function (term, page) {
                        return {
                            term: term,
                            limit: 10
                        };
                    },
                    results: function (data, page) {
                        if (data.results != null) {
                            return {results: data.results};
                        } else {
                            return {results: [{id: '', text: 'No Match Found'}]};
                        }
                    }
                }
            });
        } else {
            nsSupplier();
        }

        // Order level shipping and discount localStorage
        $('#podiscount').on("change", function (e) {
            localStorage.setItem('podiscount', $(this).val());
            loadItems();
        });
        if (podiscount = localStorage.getItem('podiscount')) {
            $('#podiscount').val(podiscount);
        }
        $('#poshipping').on("change", function (e) {
            localStorage.setItem('poshipping', $(this).val());
            loadItems();
        });
        if (poshipping = localStorage.getItem('poshipping')) {
            $('#poshipping').val(poshipping);
        }

        $('#poref').on("change", function (e) {
            localStorage.setItem('poref', $(this).val());
        });
        if (poref = localStorage.getItem('poref')) {
            $('#poref').val(poref);
        }

        $('#ponote').redactor('destroy');
        $('#ponote').redactor({
            buttons: ['formatting', '|', 'alignleft', 'aligncenter', 'alignright', 'justify', '|', 'bold', 'italic', 'underline', '|', 'unorderedlist', 'orderedlist', '|', 'link', '|', 'html'],
            formattingTags: ['p', 'pre', 'h3', 'h4'],
            minHeight: 100,
            changeCallback: function (e) {
                var v = this.get();
                localStorage.setItem('ponote', v);
            }
        });
        if (ponote = localStorage.getItem('ponote')) {
            $('#ponote').redactor('set', ponote);
        }

        $('input[type="submit"]').attr('disabled', false);

        $('select.select').select2({minimumResultsForSearch: 7});

        // Prevent default action when enter is pressed
        $('body').bind('keypress', function (e) {
            if (e.keyCode == 13) {
                e.preventDefault();
                return false;
            }
        });

        // Order tax calculation
        if (site.settings.tax2 != 0) {
            $('#potax2').change(function () {
                localStorage.setItem('potax2', $(this).val());
                loadItems();
                return;
            });
        }

        // Add Purchase items
        $(".add_po_item").on("click", function (e) {
            var mid = (new Date).getTime(),
                mcode = $(this).attr('id'),
                mqty = 1,
                mdiscount = $('#discount').val() ? $('#discount').val() : '0',
                unit_price = $(this).attr('data-price'),
                mtax = $(this).attr('data-tax'),
                munit = $(this).attr('data-unit');
            if (mcode) {
                $.ajax({
                    type: "get",
                    url: "<?= site_url('purchases/suggestions'); ?>/",
                    data: {term: mcode},
                    dataType: "json",
                    success: function (data) {
                        if (data !== null) {
                            poitems[mid] = {"id": mid, "item_id": data.id, "label": data.label, "row": {"id": data.id, "code": data.code, "name": data.name, "cost": data.cost, "qty": mqty, "base_unit": data.unit, "unit": munit, "tax_rate": mtax, "tax_method": data.tax_method, "discount": mdiscount}};
                            localStorage.setItem('poitems', JSON.stringify(poitems));
                            loadItems();
                        } else {
                            bootbox.alert('<?= lang('no_match_found') ?>');
                        }
                    }
                });
            }
        });

        // Add Manual Item
        $("#addManually").on("click", function (e) {
            if (!$('#mcode').val()) {
                $('#mError').text('<?=lang('item_code_is_required')?>');
                $('#mError-con').show();
                return false;
            }
            if (!$('#mname').val()) {
                $('#mError').text('<?=lang('product_name_is_required')?>');
                $('#mError-con').show();
                return false;
            }
            if (!$('#mcost').val()) {
                $('#mError').text('<?=lang('product_cost_is_required')?>');
                $('#mError-con').show();
                return false;
            }
            if (!$('#mquantity').val()) {
                $('#mError').text('<?=lang('product_quantity_is_required')?>');
                $('#mError-con').show();
                return false;
            }

            var mid = (new Date).getTime(),
                mcode = $('#mcode').val(),
                mname = $('#mname').val(),
                mcost = $('#mcost').val(),
                mqty = $('#mquantity').val(),
                mdiscount = $('#mdiscount').val() ? $('#mdiscount').val() : '0',
                mtax = $('#mtax').val(),
                mtax_method = $('#mtax_method').val(),
                munit = $('#munit').val();
            poitems[mid] = {"id": mid, "item_id": mid, "label": mname + ' (' + mcode + ')', "row": {"id": mid, "code": mcode, "name": mname, "cost": mcost, "qty": mqty, "base_unit": munit, "unit": munit, "tax_rate": mtax, "tax_method": mtax_method, "discount": mdiscount}};
            localStorage.setItem('poitems', JSON.stringify(poitems));
            loadItems();
        });

        // Add Purchase Item
        $("#add_item").autocomplete({
            source: '<?= site_url('purchases/suggestions'); ?>',
            minLength: 1,
            autoFocus: false,
            delay: 250,
            response: function (event, ui) {
                if ($(this).val().length >= 16 && ui.content[0].id == 0) {
                    bootbox.alert('<?= lang('no_match_found') ?>', function () {
                        $('#add_item').focus();
                    });
                    $(this).removeClass('ui-autocomplete-loading');
                    $(this).val('');
                }
                else if (ui.content.length == 1 && ui.content[0].id != 0) {
                    ui.item = ui.content[0];
                    $(this).data('ui-autocomplete')._trigger('select', 'autocompleteselect', ui);
                    $(this).autocomplete('close');
                    $(this).removeClass('ui-autocomplete-loading');
                }
                else if (ui.content.length == 1 && ui.content[0].id == 0) {
                    bootbox.alert('<?= lang('no_match_found') ?>', function () {
                        $('#add_item').focus();
                    });
                    $(this).removeClass('ui-autocomplete-loading');
                    $(this).val('');
                }
            },
            select: function (event, ui) {
                event.preventDefault();
                if (ui.item.id !== 0) {
                    var row = add_purchase_item(ui.item);
                    if (row)
                        $(this).val('');
                } else {
                    bootbox.alert('<?= lang('no_match_found') ?>');
                }
            }
        });

        $(document).on('click', '#add_item', function () {
            $(this).autocomplete("search", $(this).val());
        });

        // Clear localStorage and reload
        $('#reset').click(function (e) {
            bootbox.confirm(lang.r_u_sure, function (result) {
                if (result) {
                    if (localStorage.getItem('poitems')) {
                        localStorage.removeItem('poitems');
                    }
                    if (localStorage.getItem('podiscount')) {
                        localStorage.removeItem('podiscount');
                    }
                    if (localStorage.getItem('potax2')) {
                        localStorage.removeItem('potax2');
                    }
                    if (localStorage.getItem('poshipping')) {
                        localStorage.removeItem('poshipping');
                    }
                    if (localStorage.getItem('poref')) {
                        localStorage.removeItem('poref');
                    }
                    if (localStorage.getItem('powarehouse')) {
                        localStorage.removeItem('powarehouse');
                    }
                    if (localStorage.getItem('ponote')) {
                        localStorage.removeItem('ponote');
                    }
                    if (localStorage.getItem('posupplier')) {
                        localStorage.removeItem('posupplier');
                    }
                    if (localStorage.getItem('podate')) {
                        localStorage.removeItem('podate');
                    }
                    if (localStorage.getItem('postatus')) {
                        localStorage.removeItem('postatus');
                    }
                    if (localStorage.getItem('popayment_term')) {
                        localStorage.removeItem('popayment_term');
                    }

                    $('#modal-loading').show();
                    location.reload();
                }
            });
        });

        // Save and load the fields in and/or from localStorage
        $('#poref').change(function (e) {
            localStorage.setItem('poref', $(this).val());
        });
        if (poref = localStorage.getItem('poref')) {
            $('#poref').val(poref);
        }
        $('#ponote').redactor('destroy');
        $('#ponote').redactor({
            buttons: ['formatting', '|', 'alignleft', 'aligncenter', 'alignright', 'justify', '|', 'bold', 'italic', 'underline', '|', 'unorderedlist', 'orderedlist', '|', 'link', '|', 'html'],
            formattingTags: ['p', 'pre', 'h3', 'h4'],
            minHeight: 100,
            changeCallback: function (e) {
                var v = this.get();
                localStorage.setItem('ponote', v);
            }
        });
        if (ponote = localStorage.getItem('ponote')) {
            $('#ponote').redactor('set', ponote);
        }

        // prevent default action upon enter
        $('body').bind('keypress', function (e) {
            if ($(e.target).hasClass('redactor_editor')) {
                return true;
            }
            if (e.keyCode == 13) {
                e.preventDefault();
                return false;
            }
        });

        // Order tax calculation
        if (site.settings.tax2 != 0) {
            $('#potax2').change(function () {
                localStorage.setItem('potax2', $(this).val());
                loadItems();
                return;
            });
        }

        // Order discount calculation
        var old_podiscount;
        $('#podiscount').focus(function () {
            old_podiscount = $(this).val();
        }).change(function () {
            var new_discount = $(this).val() ? $(this).val() : '0';
            if (is_valid_discount(new_discount)) {
                localStorage.removeItem('podiscount');
                localStorage.setItem('podiscount', new_discount);
                loadItems();
                return;
            } else {
                $(this).val(old_podiscount);
                bootbox.alert('<?=lang('unexpected_value');?>');
                return;
            }

        });

        /* ----------------------
         * Delete Row Method
         * ---------------------- */
        $(document).on('click', '.podel', function () {
            var row = $(this).closest('tr');
            var item_id = row.attr('data-item-id');
            delete poitems[item_id];
            row.remove();
            if(poitems.hasOwnProperty(item_id)) { } else {
                localStorage.setItem('poitems', JSON.stringify(poitems));
                loadItems();
                return;
            }
        });

        /* --------------------------
         * Edit Row Quantity Method
         -------------------------- */
        var old_row_qty;
        $(document).on("focus", '.rquantity', function () {
            old_row_qty = $(this).val();
        }).on("change", '.rquantity', function () {
            var row = $(this).closest('tr');
            if (!is_numeric($(this).val()) || parseFloat($(this).val()) < 0) {
                $(this).val(old_row_qty);
                bootbox.alert('<?= lang('unexpected_value'); ?>');
                return;
            }
            var new_qty = parseFloat($(this).val()),
                item_id = row.attr('data-item-id');
            poitems[item_id].row.qty = new_qty;
            localStorage.setItem('poitems', JSON.stringify(poitems));
            loadItems();
        });

        /* --------------------------
         * Edit Row Cost Method
         -------------------------- */
        var old_cost;
        $(document).on("focus", '.rcost', function () {
            old_cost = $(this).val();
        }).on("change", '.rcost', function () {
            var row = $(this).closest('tr');
            if (!is_numeric($(this).val())) {
                $(this).val(old_cost);
                bootbox.alert('<?= lang('unexpected_value'); ?>');
                return;
            }
            var new_cost = parseFloat($(this).val()),
                item_id = row.attr('data-item-id');
            poitems[item_id].row.cost = new_cost;
            localStorage.setItem('poitems', JSON.stringify(poitems));
            loadItems();
        });

        $(document).on("click", '#removeReadonly', function () {
            $('#posupplier').select2('readonly', false);
            return false;
        });

        // If there is any item in localStorage
        if (localStorage.getItem('poitems')) {
            loadItems();
        }

        $(document).on('click', '.poprint', function (e) {
            e.preventDefault();
            var link = $(this).attr('href');
            $.get(link, function(data) {
                data = data.replace('<head>', '<head><title>Orçamento</title>');
                if (window.matchMedia) {
                    var mediaQueryList = window.matchMedia('print');
                    mediaQueryList.addListener(function(mql) {
                        if (!mql.matches) {
                            location.reload();
                        }
                    });
                }
                var myWindow = window.open("", "_blank", "width=800,height=600");
                myWindow.document.write(data);
                myWindow.document.close();
                myWindow.focus();
                myWindow.print();
                myWindow.close();
            });
            return false;
        });

    });

    /* -----------------------
     * Misc Actions
     ----------------------- */

    // Supplier  set focus
    function nsSupplier() {
        $('#posupplier').select2({
            minimumInputLength: 1,
            ajax: {
                url: site.base_url + "suppliers/suggestions",
                dataType: 'json',
                quietMillis: 15,
                data: function (term, page) {
                    return {
                        term: term,
                        limit: 10
                    };
                },
                results: function (data, page) {
                    if (data.results != null) {
                        return {results: data.results};
                    } else {
                        return {results: [{id: '', text: 'No Match Found'}]};
                    }
                }
            }
        });
    }

    function loadItems() {

        if (localStorage.getItem('poitems')) {
            total = 0;
            count = 1;
            an = 1;
            product_tax = 0;
            invoice_tax = 0;
            product_discount = 0;
            order_discount = 0;
            total_discount = 0;

            $('#poTable tbody').empty();
            $('#poTable tfoot').empty();
            poitems = JSON.parse(localStorage.getItem('poitems'));
            $.each(poitems, function () {

                var item = this;
                var item_id = site.settings.item_addition == 1 ? item.item_id : item.id;
                poitems[item_id] = item;

                var product_id = item.row.id, item_type = item.row.type, combo_items = item.combo_items, item_cost = item.row.cost, item_qty = item.row.qty, item_base_unit = item.row.base_unit, item_unit = item.row.unit, item_name = item.row.name, item_code = item.row.code, item_tax_method = item.row.tax_method, item_tax_rate = item.row.tax_rate, item_discount = item.row.discount;
                var unit_cost = item.row.real_unit_cost ? item.row.real_unit_cost : item.row.cost;
                var product_unit = item.row.unit, base_quantity = item.row.base_quantity;

                var pr_tax = item.tax_rate;
                var pr_tax_val = 0, pr_tax_rate = 0;
                if (site.settings.tax1 == 1) {
                    if (pr_tax !== false) {
                        if (pr_tax.type == 1) {
                            if (item_tax_method == 0) {
                                pr_tax_val = formatDecimal(((unit_cost) * parseFloat(pr_tax.rate)) / (100 + parseFloat(pr_tax.rate)), 4);
                                pr_tax_rate = formatDecimal(pr_tax.rate) + '%';
                            } else {
                                pr_tax_val = formatDecimal(((unit_cost) * parseFloat(pr_tax.rate)) / 100, 4);
                                pr_tax_rate = formatDecimal(pr_tax.rate) + '%';
                            }
                        } else if (pr_tax.type == 2) {
                            pr_tax_val = parseFloat(pr_tax.rate);
                            pr_tax_rate = pr_tax.rate;
                        }
                    }
                }
                product_tax += pr_tax_val * item_qty;

                item_cost = item_tax_method == 0 ? formatDecimal(unit_cost-pr_tax_val, 4) : formatDecimal(unit_cost);
                unit_cost = formatDecimal(unit_cost+item_discount, 4);
                var sel_opt = '';
                $.each(item.options, function () {
                    if(this.id == item.row.option) {
                        sel_opt = this.name;
                    }
                });

                var row_no = (new Date).getTime();
                var newTr = $('<tr id="row_' + row_no + '" class="row_' + item_id + '" data-item-id="' + item_id + '"></tr>');
                tr_html = '<td><input name="product_id[]" type="hidden" class="rid" value="' + product_id + '"><input name="product[]" type="hidden" class="rcode" value="' + item_code + '"><input name="product_name[]" type="hidden" class="rname" value="' + item_name + '"><span class="sname" id="name_' + row_no + '">' + item_code + ' - ' + item_name + (sel_opt != '' ? ' ('+sel_opt+')' : '') + '</span></td>';
                tr_html += '<td><input class="form-control input-sm text-right rquantity" name="quantity[]" type="text" value="' + formatQuantity2(item_qty) + '" data-id="' + row_no + '" data-item="' + item_id + '" id="quantity_' + row_no + '" onClick="this.select();"></td>';
                tr_html += '<td><input class="form-control input-sm text-right rcost" name="cost[]" type="text" value="' + formatMoney(unit_cost) + '" data-id="' + row_no + '" data-item="' + item_id + '" id="cost_' + row_no + '" onClick="this.select();"></td>';
                tr_html += '<td><input class="form-control input-sm text-right rdiscount" name="product_discount[]" type="text" id="discount_' + row_no + '" value="' + (item_discount != 0 ? formatMoney(item_discount) : '') + '" onClick="this.select();"></td>';
                if (site.settings.tax1 == 1) {
                    tr_html += '<td><input class="form-control input-sm text-right" name="product_tax[]" type="hidden" id="product_tax_' + row_no + '" value="' + pr_tax.id + '"><span class="text-right sproduct_tax" id="sproduct_tax_' + row_no + '">' + (pr_tax_rate ? pr_tax_rate : (site.settings.default_tax_rate != 0 ? site.settings.default_tax_rate : '')) + '</span></td>';
                }
                tr_html += '<td><span class="text-right ssubtotal" id="subtotal_' + row_no + '">' + formatMoney(((parseFloat(item_cost) + parseFloat(pr_tax_val)) * parseFloat(item_qty))) + '</span></td>';
                tr_html += '<td class="text-center"><i class="fa fa-times tip podel" id="' + row_no + '" title="Remove" style="cursor:pointer;"></i></td>';
                newTr.html(tr_html);
                newTr.prependTo('#poTable');
                total += formatDecimal(((parseFloat(item_cost) + parseFloat(pr_tax_val)) * parseFloat(item_qty)), 4);
                count += parseFloat(item_qty);
                an++;
            });

            var col = 4;
            if (site.settings.tax1 == 1) { col++; }
            var tfoot = '<tr id="tfoot" class="tfoot active"><th colspan="2">Total</th><th class="text-center">' + formatQty(parseFloat(count) - 1) + '</th><th class="text-right">'+formatMoney(product_tax)+'</th><th class="text-right">'+formatMoney(total)+'</th><th class="text-center"><i class="fa fa-trash-o" style="opacity:0.5; filter:alpha(opacity=50);"></i></th></tr>';
            $('#poTable tfoot').html(tfoot);

            // Order level discount calculations
            if (podiscount = localStorage.getItem('podiscount')) {
                var ds = podiscount;
                if (ds.indexOf("%") !== -1) {
                    var pds = ds.split("%");
                    if (!isNaN(pds[0])) {
                        order_discount = formatDecimal((((total) * parseFloat(pds[0])) / 100), 4);
                    } else {
                        order_discount = formatDecimal(ds);
                    }
                } else {
                    order_discount = formatDecimal(ds);
                }

                total_discount = parseFloat(order_discount) + product_discount;
                $('#tds').text(formatMoney(order_discount));
                $('#discount').val(podiscount);
            }

            // Order level tax calculations
            if (site.settings.tax2 != 0) {
                if (potax2 = localStorage.getItem('potax2')) {
                    $.each(tax_rates, function () {
                        if (this.id == potax2) {
                            if (this.type == 1) {
                                invoice_tax = formatDecimal((((total - order_discount) * this.rate) / 100), 4);
                                tax_rate = formatDecimal(this.rate) + '%';
                            } else if (this.type == 2) {
                                invoice_tax = parseFloat(this.rate);
                                tax_rate = this.rate;
                            }
                        }
                    });
                }
            }

            total_discount = parseFloat(order_discount) + product_discount;
            // Totals calculations after item addition
            var gtotal = total - order_discount + shipping + invoice_tax;
            $('#total').text(formatMoney(total));
            $('#titems').text((an-1)+' ('+(formatQty(parseFloat(count) - 1))+' items)');
            $('#tds').text(formatMoney(order_discount));
            if (site.settings.tax2 != 0) {
                $('#ttax2').text(formatMoney(invoice_tax));
            }
            $('#tship').text(formatMoney(shipping));
            $('#gtotal').text(formatMoney(gtotal));
            if (an > parseInt(site.settings.bc_fix) && parseInt(site.settings.bc_fix) > 0) {
                $('html, body').animate({scrollTop: $('#sticker').offset().top}, 500);
                $(window).scrollTop($(window).scrollTop() + 1);
            }
            set_page_focus();
        }
    }

    /* -----------------------------
     * Add Purchase Item Function
     * @param {json} item
     * @returns {Boolean}
     ---------------------------- */
    function add_purchase_item(item) {
        if (count == 1) {
            poitems = {};
            if ($('#posupplier').val()) {
                $('#posupplier').select2("readonly", true);
            } else {
                bootbox.alert('<?= lang('select_above'); ?>');
                item = null;
                return;
            }
        }
        if (item == null)
            return;

        var item_id = site.settings.item_addition == 1 ? item.item_id : item.id;
        if (poitems[item_id]) {
            poitems[item_id].row.qty = parseFloat(poitems[item_id].row.qty) + 1;
        } else {
            poitems[item_id] = item;
        }
        localStorage.setItem('poitems', JSON.stringify(poitems));
        loadItems();
        return true;
    }

    if (typeof (Storage) === "undefined") {
        $(window).bind('beforeunload', function (e) {
            if (count > 1) {
                var message = "You will loss data!";
                return message;
            }
        });
    }

    function initOrcamento() {
        load_orcamento_items();
    }

    function load_orcamento_items() {
        if (localStorage.getItem('poitems')) {
            localStorage.removeItem('poitems');
        }
        if (localStorage.getItem('podiscount')) {
            localStorage.removeItem('podiscount');
        }
        if (localStorage.getItem('potax2')) {
            localStorage.removeItem('potax2');
        }
        if (localStorage.getItem('poshipping')) {
            localStorage.removeItem('poshipping');
        }

        <?php if ($purchase) { ?>
        localStorage.setItem('posupplier', '<?= $purchase->supplier_id; ?>');
        localStorage.setItem('poref', '<?= $purchase->reference; ?>');
        localStorage.setItem('podate', '<?= $purchase->date; ?>');
        localStorage.setItem('ponote', '<?= str_replace(array("\r", "\n"), '', $this->sma->decode_html($purchase->note)); ?>');
        localStorage.setItem('podiscount', '<?= $purchase->order_discount; ?>');
        localStorage.setItem('potax2', '<?= $purchase->order_tax_id; ?>');
        localStorage.setItem('poshipping', '<?= $purchase->shipping; ?>');

        <?php if ($purchase_items) { ?>
        var poitems = {};
        <?php foreach ($purchase_items as $item) { ?>
        var row = {
            id: '<?= $item->id; ?>',
            item_id: '<?= $item->product_id; ?>',
            label: '<?= $item->product_code . ' - ' . $item->product_name; ?>',
            row: {
                id: '<?= $item->product_id; ?>',
                code: '<?= $item->product_code; ?>',
                name: '<?= $item->product_name; ?>',
                cost: '<?= $item->unit_cost; ?>',
                qty: '<?= $item->quantity; ?>',
                base_unit: '<?= $item->unit; ?>',
                unit: '<?= $item->unit; ?>',
                tax_method: '<?= $item->product_tax_method; ?>',
                tax_rate: <?= $item->tax_rate_id ? $item->tax_rate_id : 'false'; ?>,
                discount: '<?= $item->discount ? $item->discount : 0; ?>'
            }
        };
        <?php if ($item->tax_rate_id) { ?>
        row.tax_rate = <?= $item->tax_rate_id; ?>;
        <?php } ?>
        poitems[<?= $item->id; ?>] = row;
        <?php } ?>
        localStorage.setItem('poitems', JSON.stringify(poitems));
        <?php } ?>
        <?php } ?>
        loadItems();
    }

    $(document).ready(function () {
        initOrcamento();
    });
</script>