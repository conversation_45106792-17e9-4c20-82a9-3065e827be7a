<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>Cupom - Orçamento <?= $orcamento->reference ?></title>
    <style>
        @media print {
            body { margin: 0; }
            .no-print { display: none !important; }
        }
        
        body {
            font-family: 'Courier New', monospace;
            font-size: 12px;
            line-height: 1.4;
            margin: 20px;
            background: white;
        }
        
        .cupom {
            width: 300px;
            margin: 0 auto;
            border: 1px solid #ddd;
            padding: 15px;
            background: white;
        }
        
        .header {
            text-align: center;
            border-bottom: 1px dashed #333;
            padding-bottom: 10px;
            margin-bottom: 15px;
        }
        
        .company-name {
            font-weight: bold;
            font-size: 14px;
            margin-bottom: 5px;
        }
        
        .company-info {
            font-size: 10px;
            line-height: 1.2;
        }
        
        .section {
            margin: 15px 0;
        }
        
        .section-title {
            font-weight: bold;
            text-align: center;
            margin: 10px 0 5px 0;
            text-decoration: underline;
        }
        
        .info-row {
            display: flex;
            justify-content: space-between;
            margin: 3px 0;
        }
        
        .info-label {
            font-weight: bold;
        }
        
        .items-table {
            width: 100%;
            border-collapse: collapse;
            margin: 10px 0;
        }
        
        .items-table th,
        .items-table td {
            text-align: left;
            padding: 3px 0;
            border-bottom: 1px dotted #333;
        }
        
        .total-section {
            border-top: 1px dashed #333;
            padding-top: 10px;
            margin-top: 15px;
        }
        
        .total-row {
            display: flex;
            justify-content: space-between;
            margin: 3px 0;
        }
        
        .total-final {
            font-weight: bold;
            font-size: 14px;
            border-top: 1px solid #333;
            padding-top: 5px;
            margin-top: 5px;
        }
        
        .footer {
            text-align: center;
            font-size: 10px;
            margin-top: 15px;
            border-top: 1px dashed #333;
            padding-top: 10px;
        }
        
        .no-print {
            text-align: center;
            margin: 20px 0;
        }
        
        .btn {
            background: #007bff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            margin: 0 5px;
            text-decoration: none;
            display: inline-block;
        }
        
        .btn-print {
            background: #28a745;
        }
        
        .btn-back {
            background: #6c757d;
        }
    </style>
</head>
<body>
    <div class="no-print">
        <button onclick="window.print()" class="btn btn-print">🖨️ Imprimir</button>
        <a href="<?= site_url('purchases/orcamentos') ?>" class="btn btn-back">← Voltar</a>
    </div>

    <div class="cupom">
        <!-- Cabeçalho da Empresa -->
        <div class="header">
            <div class="company-name">Ópera Perfumaria e Cosméticos</div>
            <div class="company-info">
                CNPJ: 53.012.469/0001-07<br>
                Pedro Gusso, nº 1089 - loja 4<br>
                Curitiba-PR<br>
                @opera.perfumaria
            </div>
        </div>

        <!-- Informações do Orçamento -->
        <div class="section">
            <div class="info-row">
                <span class="info-label">Nome:</span>
                <span><?= $orcamento->supplier_name ?></span>
            </div>
            <div class="info-row">
                <span class="info-label">Telefone:</span>
                <span><?= $orcamento->pos_customer_phone ?: 'Não informado' ?></span>
            </div>
            <div class="info-row">
                <span class="info-label">Pedido:</span>
                <span><?= $orcamento->reference ?></span>
            </div>
            <div class="info-row">
                <span class="info-label">Data:</span>
                <span><?= date('d/m/Y H:i:s', strtotime($orcamento->date)) ?></span>
            </div>
            <div class="info-row">
                <span class="info-label">Vendedor:</span>
                <span><?= $orcamento->pos_seller_name ?: 'Não informado' ?></span>
            </div>
        </div>

        <!-- Produtos -->
        <div class="section">
            <div class="section-title">DESCRIÇÃO</div>
            <table class="items-table">
                <thead>
                    <tr>
                        <th>Produto</th>
                    </tr>
                </thead>
                <tbody>
                    <?php if (!empty($itens)): ?>
                        <?php foreach ($itens as $item): ?>
                            <tr>
                                <td><?= $item->product_name ?></td>
                            </tr>
                        <?php endforeach; ?>
                    <?php else: ?>
                        <tr><td>Produtos não especificados</td></tr>
                    <?php endif; ?>
                </tbody>
            </table>
        </div>

        <!-- Totais -->
        <div class="total-section">
            <div class="total-row">
                <span>Qtd de itens:</span>
                <span><?= $orcamento->total_items ?: 1 ?></span>
            </div>
            <div class="total-row">
                <span>Valor Total:</span>
                <span>R$ <?= number_format($orcamento->total ?: $orcamento->grand_total, 2, ',', '.') ?></span>
            </div>
        </div>

        <!-- Pagamento -->
        <div class="section">
            <div class="section-title">PAGAMENTO</div>
            <div class="total-row">
                <span>Forma de pagamento:</span>
                <span><?= $orcamento->pos_payment_method ?: 'Não informado' ?></span>
            </div>
            <div class="total-row">
                <span>Valor pago (entrada):</span>
                <span>R$ <?= number_format($orcamento->pos_amount_paid ?: 0, 2, ',', '.') ?></span>
            </div>
            <div class="total-row total-final">
                <span>Saldo a Pagar:</span>
                <span>R$ <?= number_format($orcamento->pos_balance_due ?: $orcamento->grand_total, 2, ',', '.') ?></span>
            </div>
        </div>

        <!-- Rodapé -->
        <div class="footer">
            NÃO É DOCUMENTO FISCAL<br>
            NÃO É VÁLIDO COMO GARANTIA DE MERCADORIA<br>
            <?= date('d/m/Y H:i:s') ?>
        </div>
    </div>

    <div class="no-print" style="margin-top: 20px;">
        <p><strong>Informações do Sistema:</strong></p>
        <ul style="text-align: left; max-width: 400px; margin: 0 auto;">
            <li><strong>ID do Orçamento:</strong> <?= $orcamento->id ?></li>
            <li><strong>Criado em:</strong> <?= date('d/m/Y H:i:s', strtotime($orcamento->date)) ?></li>
            <li><strong>Criado por:</strong> Usuário ID <?= $orcamento->created_by ?></li>
            <?php if ($orcamento->note): ?>
                <li><strong>Observações:</strong><br>
                    <textarea readonly style="width: 100%; height: 100px; font-size: 11px;"><?= $orcamento->note ?></textarea>
                </li>
            <?php endif; ?>
        </ul>
    </div>
</body>
</html>
